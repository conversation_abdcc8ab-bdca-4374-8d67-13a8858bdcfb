<template>
  <div class="select-none">
    <!-- Ren<PERSON>ton -->
    <div class="flex justify-center items-center gap-2">
      <ConsoleLog size="small" />
      <div>
        <a-button type="primary" @click="showRenderModal" :disabled="!canRender || isProcessing"
          :loading="isProcessing">
          Render
        </a-button>
      </div>
    </div>

    <!-- Render Options Modal -->
    <a-modal v-model:open="modalVisible" title="Video Render Options" @ok="handleRenderConfirm"
      @cancel="handleModalCancel" :confirm-loading="isProcessing" width="90%"
      :body-style="{ maxHeight: '80vh', overflow: 'hidden' }" class="select-none">
      <a-row :gutter="24">
        <!-- Video Preview Column -->
        <a-col :span="12" class="flex h-full overflow-auto">
          <VideoPreviewDraw ref="videoPreviewDraw"
            :video-src="ttsStore.currentSrtList?.path.replace('-ocr.srt', '.mp4').replace('.srt', '.mp4')"
            :render-options="renderOptions" :time-range="timeRange" :video-duration="videoDuration"
            :current-time="currentTime" @blur-areas-updated="handleBlurAreasUpdated"
            @time-range-updated="handleTimeRangeUpdated" @video-loaded="onVideoLoaded"
            @video-time-update="onVideoTimeUpdate" />
        </a-col>

        <!-- Options Column -->
        <a-col :span="12" style="overflow: auto; height: 90vh;">
          <a-form :model="renderOptions" layout="vertical">
            <!-- Text Options -->
            <TextAnimation />
            <a-tabs v-model:activeKey="ttsStore.activeTabChild">
              <a-tab-pane key="subtitle" tab="Subtitle">
                <SubtitleSettings />
              </a-tab-pane>
              <a-tab-pane key="subtitle-voice" tab="Subtitle Voice">
                <SubtitleSettingVoice />
              </a-tab-pane>
            </a-tabs>

            <!-- Logo Options -->
            <!-- <a-form-item label="Logo Settings">
              <a-checkbox v-model:checked="renderOptions.showLogo">
                Add logo/watermark
              </a-checkbox>
              <div v-if="renderOptions.showLogo" class="mt-2">
                <a-row :gutter="16">
                  <a-col :span="12">
                    <a-form-item label="Logo Position">
                      <a-select v-model:value="renderOptions.logoPosition">
                        <a-select-option value="top-left">Top Left</a-select-option>
                        <a-select-option value="top-right">Top Right</a-select-option>
                        <a-select-option value="bottom-left">Bottom Left</a-select-option>
                        <a-select-option value="bottom-right">Bottom Right</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item label="Logo Size">
                      <a-select v-model:value="renderOptions.logoSize">
                        <a-select-option value="small">Small</a-select-option>
                        <a-select-option value="medium">Medium</a-select-option>
                        <a-select-option value="large">Large</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                </a-row>
                <a-form-item label="Logo File">
                  <a-upload v-model:file-list="logoFileList" :before-upload="beforeLogoUpload" accept="image/*"
                    :max-count="1">
                    <a-button>
                      <upload-outlined />
                      Select Logo
                    </a-button>
                  </a-upload>
                </a-form-item>
              </div>
            </a-form-item> -->

            <!-- Audio Options -->
            <a-form-item label="Audio Settings" class="border border-gray-600 p-2 rounded-lg">
              <a-checkbox v-model:checked="renderOptions.addBackgroundMusic">
                Add background music
              </a-checkbox>
              <div v-if="renderOptions.addBackgroundMusic" class="mt-2 mx-2">
                <a-form-item label="Background Music File">
                  <a-upload v-model:file-list="musicFileList" :before-upload="beforeMusicUpload" accept="audio/*"
                    :max-count="1">
                    <a-button>
                      <upload-outlined />
                      Select Music
                    </a-button>
                  </a-upload>
                </a-form-item>
                <a-form-item label="Background Music Volume">
                  <a-slider v-model:value="renderOptions.backgroundMusicVolume" :min="0" :max="100"
                    :marks="{ 0: '0%', 25: '25%', 50: '50%', 75: '75%', 100: '100%' }" />
                </a-form-item>
              </div>

              <a-form-item label="Âm thanh video gốc" class="mt-2">
                <a-checkbox v-model:checked="renderOptions.holdMusicOnly">
                  Chỉ giữ lại nhạc nền
                </a-checkbox>
                <a-checkbox v-model:checked="renderOptions.holdOriginalAudio">
                  Giữ lại âm thanh video gốc
                </a-checkbox>
              </a-form-item>
              <!-- âm lượng âm thanh video gốc -->
              <a-form-item label="Âm lượng âm thanh video gốc" v-if="renderOptions.holdOriginalAudio">
                <a-slider v-model:value="renderOptions.originalAudioVolume" :min="0" :max="100"
                  :marks="{ 0: '0%', 25: '25%', 50: '50%', 75: '75%', 100: '100%' }" />
              </a-form-item>
            </a-form-item>

            <!-- Output Options -->
            <a-form-item label="Output Settings" >
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="Video Quality">
                    <a-select v-model:value="renderOptions.videoQuality">
                      <!-- custom -->
                      <a-select-option value="custom">Custom</a-select-option>
                      <a-select-option value="720p/16:9">720p HD</a-select-option>
                      <a-select-option value="1080p/16:9">Full HD</a-select-option>
                      <a-select-option value="4k/16:9">4K Ultra HD</a-select-option>
                      <a-select-option value="1080p/9:16">Full HD Dọc</a-select-option>
                      <a-select-option value="4k/9:16">4K Ultra HD Dọc</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="Frame Rate">
                    <a-select v-model:value="renderOptions.frameRate">
                      <a-select-option value="24">24 fps</a-select-option>
                      <a-select-option value="30">30 fps</a-select-option>
                      <a-select-option value="60">60 fps</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item :label="`Zoom Video: ${Math.round(renderOptions.scaleFactor * 100)}%`">
                    <a-slider
                      v-model:value="renderOptions.scaleFactor"
                      :min="1"
                      :max="2"
                      :step="0.1"
                      size="small"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="Flip Video">
                  <a-checkbox
                    id="flipIntroVideo"
                    v-model:checked="renderOptions.flipVideo"
                    class="cursor-pointer"
                  />
                  </a-form-item>
                </a-col>
              </a-row>
              <a-row :gutter="16" class="mb-32">
                <a-col :span="12">
                  <a-form-item :label="`Volume Video: ${renderOptions.volume}%`">
                    <a-slider
                      v-model:value="renderOptions.volume"
                      :min="-0.5"
                      :max="5"
                      :step="0.1"
                      size="small"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form-item>
          </a-form>
        </a-col>
      </a-row>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, reactive, nextTick, onMounted, onUnmounted, watch } from 'vue';
import { message } from 'ant-design-vue';
import {
  UploadOutlined,
  // BlurOutlined,
  DeleteOutlined,
  FontColorsOutlined,
  ClearOutlined,
  UndoOutlined,
  DragOutlined
} from '@ant-design/icons-vue';
import { useTTSStore } from '@/stores/ttsStore';
import VideoPreviewDraw from './VideoPreviewDraw.vue';
import { useSubtitleStore } from '@/stores/subtitle-store';
import SubtitleSettings from './SubtitleSettings.vue';
import SubtitleSettingVoice from './SubtitleSettingVoice.vue';
import TextAnimation from './TextAnimation.vue';
import { state } from '@/lib/state';
import fontService from '@/services/fontService';

const ttsStore = useTTSStore();
const isProcessing = ref(false);
const modalVisible = ref(false);
const logoFileList = ref([]);
const musicFileList = ref([]);



// Video preview refs
const videoPreviewDraw = ref(null);
const videoDuration = ref(0);
const currentTime = ref(0);
const blurAreas = ref([]);



// Time range for applying effects
const timeRange = reactive({
  start: 0,
  end: 0
});

const subtitleStore = useSubtitleStore();

const renderOptions = subtitleStore.renderOptions


// Render options configuration
// const renderOptions = reactive({
//   // Text options
//   showText: true,
//   fontSize: 24,
//   textColor: '#fff700',
//   textValue: '@Hello World',
//   textOpacity: 50,
//   textDirection: 'random',

//   // Text Subtitle options
//   showSubtitle: true,
//   subtitleFontSize: 48,
//   subtitleTextColor: '#ffffff',
//   subtitleBackgroundColor: '#000000',
//   subtitleBorderColor: '#000000',
//   subtitleBold: true,
//   shadowSize: 2,
//   assOptions: {
//     posX: 0,
//     posY: 33,
//     rotation: 0,
//     align: 5,
//     fontSize: 48,
//   },

//   // Logo options
//   showLogo: false,
//   logoPosition: 'bottom-right',
//   logoSize: 'medium',

//   // Audio options
//   addBackgroundMusic: false,
//   backgroundMusicVolume: 30,
//   originalAudioVolume: 80,
//   holdOriginalAudio: false,
//   holdMusicOnly: false,

//   // Output options
//   videoQuality: '1080p/16:9',
//   frameRate: '30'
// });

const canRender = computed(() => {
  return ttsStore.currentSrtList && ttsStore.currentSrtList.items.length > 0;
});



// Convert CSS color to ASS color format
function cssToASSColor(cssColor, opacity = '00') {
  // Remove # if present
  const hex = cssColor.replace('#', '');

  if (hex === 'transparent' || hex === '') {
    return '&HFF000000';
  }

  // Convert hex to RGB
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);

  // ASS format: &H + opacity + BB + GG + RR (BGR format)
  const assColor = `&H${opacity}${b.toString(16).padStart(2, '0').toUpperCase()}${g.toString(16).padStart(2, '0').toUpperCase()}${r.toString(16).padStart(2, '0').toUpperCase()}`;

  return assColor;
}



async function getVideoInfo() {
  const info = ttsStore.currentSrtList?.info || {}
  console.log('Video info:', info);
  const { width, height } = info;
  const isPortrait = height > width;
  if (isPortrait) {
    subtitleStore.renderOptions.videoQuality = '1080p/9:16';
  } else {
    subtitleStore.renderOptions.videoQuality = '1080p/16:9';
  }
}


function showRenderModal() {
  if (!canRender.value) return;
  modalVisible.value = true;
  // getVideoInfo();
}

function handleModalCancel() {
  modalVisible.value = false;
}

function beforeLogoUpload(file) {
  // Validate logo file
  const isImage = file.type.startsWith('image/');
  if (!isImage) {
    message.error('Please select a valid image file!');
    return false;
  }

  const isLt5M = file.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    message.error('Logo file must be smaller than 5MB!');
    return false;
  }

  renderOptions.logoFile = file.path;
  return false; // Prevent auto upload
}

function beforeMusicUpload(file) {
  // Validate music file
  const isAudio = file.type.startsWith('audio/');
  console.log('isAudio', file);
  if (!isAudio) {
    message.error('Please select a valid audio file!');
    return false;
  }

  const isLt50M = file.size / 1024 / 1024 < 50;
  if (!isLt50M) {
    message.error('Music file must be smaller than 50MB!');
    return false;
  }
  console.log('beforeMusicUpload', file);
  
  renderOptions.musicFile = file.path;
  return false; // Prevent auto upload
}
const isVoices = ref(false);
// Event handlers for VideoPreviewDraw component
function handleBlurAreasUpdated(data) {
  // Handle both old format (array) and new format (object with areas and assOptions)
  if (Array.isArray(data)) {
    blurAreas.value = data;
  } else {
    blurAreas.value = data.areas || [];
    // Store ASS options for video coordinates
    if (data.isVoice) {
      isVoices.value = data.isVoice;
    }
    if (data.assOptions) {
      renderOptions.assOptionsForVideo = data.assOptions;
    }
  }
}

function handleTimeRangeUpdated(range) {
  timeRange.start = range.start;
  timeRange.end = range.end;
}

function onVideoLoaded(video) {
  videoDuration.value = video.duration;
  timeRange.end = video.duration;
}

function onVideoTimeUpdate(time) {
  currentTime.value = time;
}

async function handleRenderConfirm() {
  isProcessing.value = true;

  try {
    // check audioUrl 
    const voiceConfigs = subtitleStore.renderVoiceOptions;
    // load config cho từng voice
    if (ttsStore.activeTabChild === "subtitle-voice") {
      // load config cho từng voice
      ttsStore.currentSrtList.items.forEach(item => {
        const voiceNumber = item.isVoice;
        if (voiceNumber > 0) {
          const voiceKey = `isVoice${voiceNumber}`;
          const voiceConfig = voiceConfigs[voiceKey];
          const textSubtitle = {
            enabled: voiceConfig.showSubtitle,
            fontSize: voiceConfig.subtitleFontSize,
            color: voiceConfig.subtitleTextColor,
            backgroundColor: voiceConfig.subtitleBackgroundColor,
            borderColor: voiceConfig.subtitleBorderColor,
            bold: voiceConfig.subtitleBold,
            fontFamily: voiceConfig.fontFamily || renderOptions.fontFamily || 'Arial',
            // ASS format colors for advanced subtitle rendering
            assColors: {
              text: cssToASSColor(voiceConfig.subtitleTextColor),
              background: cssToASSColor(voiceConfig.subtitleBackgroundColor),
              border: cssToASSColor(voiceConfig.subtitleBorderColor),
              shadowSize: voiceConfig.shadowSize
            },
            // ASS options with video coordinates
            assOptions: isVoices.value[voiceKey] || voiceConfig.assOptions
          }
          item.textSubtitleSettings = textSubtitle;

        }
      });
    } else {
      // load config cho toàn bộ subtitle
      ttsStore.currentSrtList.items.forEach(item => {
        // delete item.textSubtitleSettings;
        delete item.textSubtitleSettings;
      });
    }



    const srtItemsClone = JSON.parse(JSON.stringify(ttsStore.currentSrtList.items));
    const currentSrtListFound = srtItemsClone.filter(e => e.isEnabled && e.audioUrl)
    console.log('currentSrtListFound', currentSrtListFound);

    if (!currentSrtListFound.length) {
      message.error('Please generate audio for all subtitles!');
      isProcessing.value = false;
      return;
    }
    const renderConfig = {
      srtItems: currentSrtListFound,
      srtPath: ttsStore.currentSrtList.path,
      blurAreas: blurAreas.value,
      timeRange: timeRange,
      options: {
        textAnimation: {
          enabled: renderOptions.showText,
          fontSize: renderOptions.fontSize,
          color: renderOptions.textColor,
          value: renderOptions.textValue,
          opacity: renderOptions.textOpacity / 100,
          directionType: renderOptions.textDirection,
          textSpeed: renderOptions.textSpeed,
          fontFamily: renderOptions.textFontFamily || 'Arial',
          fontPath: fontService.getFontPath(renderOptions.textFontFamily || 'Arial')
        },
        textSubtitle: {
          enabled: renderOptions.showSubtitle,
          fontSize: renderOptions.subtitleFontSize,
          color: renderOptions.subtitleTextColor,
          backgroundColor: renderOptions.subtitleBackgroundColor,
          borderColor: renderOptions.subtitleBorderColor,
          bold: renderOptions.subtitleBold,
          fontFamily: renderOptions.fontFamily || 'Arial',
          fontPath: fontService.getFontPath(renderOptions.fontFamily || 'Arial'),
          // ASS format colors for advanced subtitle rendering
          assColors: {
            text: cssToASSColor(renderOptions.subtitleTextColor),
            background: cssToASSColor(renderOptions.subtitleBackgroundColor),
            border: cssToASSColor(renderOptions.subtitleBorderColor),
            shadowSize: renderOptions.shadowSize
          },
          // ASS options with video coordinates
          assOptions: renderOptions.assOptionsForVideo || renderOptions.assOptions
        },
        logo: {
          enabled: renderOptions.showLogo,
          position: renderOptions.logoPosition,
          size: renderOptions.logoSize,
          file: renderOptions.logoFile
        },
        audio: {
          backgroundMusic: {
            enabled: renderOptions.addBackgroundMusic,
            file: renderOptions.musicFile,
            volume: renderOptions.backgroundMusicVolume / 100
          },
          originalVolume: renderOptions.originalAudioVolume / 100,
          holdOriginalAudio: renderOptions.holdOriginalAudio,
          holdMusicOnly: renderOptions.holdMusicOnly
        },
        output: {
          quality: renderOptions.videoQuality,
          frameRate: parseInt(renderOptions.frameRate),
          scaleFactor: renderOptions.scaleFactor,
          flipVideo: renderOptions.flipVideo,
          volume: renderOptions.volume
        }
      }
    };
    console.log('renderConfig', renderConfig);
    // Call the electron API with enhanced options
    electronAPI.processVideoWithOptions(JSON.parse(JSON.stringify(renderConfig)));

    // Simulate processing time
    await new Promise((resolve) => setTimeout(resolve, 2000));

    message.success('Video rendered successfully');
    modalVisible.value = false;

  } catch (error) {
    message.error('Error rendering video: ' + error.message);
  } finally {
    isProcessing.value = false;
  }
}
let removeListener = null



onMounted(async () => {
  // Load fonts
  await fontService.loadFonts();

  removeListener= window.electronAPI.on('video-task', (event, data) => {
    if(data?.success){
      message.success(data.data);
    }
  });
});

onUnmounted(() => {
  if (removeListener) {
    removeListener();
  }
});

watch(
  () => ttsStore.activeTabChild,
  async (newVal) => {
    if (newVal === "subtitle") {
      message.info('Đã chọn cấu hình toàn bộ subtitle cho cả video ');
    }
    if (newVal === "subtitle-voice") {
      message.info('Đã chọn cấu hình riêng cho từng voice ');
    }
  }
);




</script>

<style scoped>
.mt-2 {
  margin-top: 8px;
}
</style>