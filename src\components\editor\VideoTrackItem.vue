<template>
  <div
    class="video-track-item absolute cursor-pointer select-none"
    :class="{
      'selected': isSelected,
      'dragging': isDragging,
      'resizing': isResizing,
      'hover': isHovered,
      'playing': isPlayingVideo
    }"
    :style="itemStyle"
    @mousedown="handleMouseDown"
    @mouseenter="isHovered = true"
    @mouseleave="isHovered = false"
    @contextmenu="handleContextMenu"
  >
    <!-- Left resize handle -->
    <div
      class="resize-handle resize-left absolute left-0 top-0 bottom-0 w-2 cursor-ew-resize opacity-0 hover:opacity-100 bg-green-500"
      @mousedown.stop="handleResizeStart('left', $event)"
      @mouseenter="handleResizeHover('left')"
      @mouseleave="handleResizeLeave('left')"
    ></div>

    <!-- Video thumbnail/preview -->
    <div v-if="hasVideoSource" class="absolute inset-0 opacity-30">
      <div class="w-full h-full bg-gradient-to-r from-green-600 to-green-400 rounded-sm">
        <!-- Video preview placeholder -->
        <div class="flex items-center justify-center h-full">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="white" opacity="0.7">
            <polygon points="5,3 19,12 5,21"/>
          </svg>
        </div>
      </div>
    </div>

    <!-- Item content -->
    <div class="item-content h-full px-2 py-1 overflow-hidden relative z-10">
      <!-- Item header -->
      <div class="flex items-center justify-between text-xs mb-1">
        <div class="flex items-center gap-2">
          <span class="font-medium text-white">{{ item.name || `Video ${item.id}` }}</span>
          <!-- Video playback controls -->
          <div class="flex items-center gap-1">
            <button
              @click.stop="toggleVideoPlayback"
              class="video-play-btn w-4 h-4 flex items-center justify-center rounded-full bg-green-600 hover:bg-green-500 transition-colors"
              :title="getPlayButtonTitle()"
            >
              <svg v-if="!isPlayingVideo" width="8" height="8" viewBox="0 0 24 24" fill="white">
                <polygon points="5,3 19,12 5,21"/>
              </svg>
              <svg v-else width="8" height="8" viewBox="0 0 24 24" fill="white">
                <rect x="6" y="4" width="4" height="16"/>
                <rect x="14" y="4" width="4" height="16"/>
              </svg>
            </button>
          </div>
        </div>
        <span class="text-gray-300">{{ formatDuration(duration) }}</span>
      </div>

      <!-- Item details -->
      <div class="text-xs text-gray-200 leading-tight line-clamp-2">
        {{ displayText }}
      </div>

      <!-- Video properties indicators -->
      <div class="flex items-center gap-1 mt-1">
        <div
          v-if="item.properties?.volume !== undefined"
          class="w-2 h-2 bg-blue-500 rounded-full"
          :title="`Volume: ${item.properties.volume}%`"
        ></div>
        <div
          v-if="item.properties?.speed !== 1"
          class="w-2 h-2 bg-yellow-500 rounded-full"
          :title="`Speed: ${item.properties.speed}x`"
        ></div>
        <div
          v-if="item.properties?.filters && Object.keys(item.properties.filters).length > 0"
          class="w-2 h-2 bg-purple-500 rounded-full"
          title="Has filters"
        ></div>
      </div>
    </div>

    <!-- Right resize handle -->
    <div
      class="resize-handle resize-right absolute right-0 top-0 bottom-0 w-2 cursor-ew-resize opacity-0 hover:opacity-100 bg-green-500"
      @mousedown.stop="handleResizeStart('right', $event)"
      @mouseenter="handleResizeHover('right')"
      @mouseleave="handleResizeLeave('right')"
    ></div>

    <!-- Selection indicator -->
    <div
      v-if="isSelected"
      class="absolute inset-0 border-2 border-green-500 pointer-events-none"
    ></div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useTimelineStore } from '@/stores/timeline-store'
import { useVideoLayersStore } from '@/stores/video-layers-store'

const props = defineProps({
  item: {
    type: Object,
    required: true
  }
})

const emit = defineEmits([
  'select',
  'drag-start',
  'drag',
  'drag-end',
  'resize-start',
  'resize',
  'resize-end',
  'context-menu',
  'video-play',
  'video-pause',
  'seek-to-time'
])

const timelineStore = useTimelineStore()
const videoLayersStore = useVideoLayersStore()

// Local state
const isHovered = ref(false)
const isDragging = ref(false)
const isResizing = ref(false)
const dragStartX = ref(0)

// Video playback state
const isPlayingVideo = ref(false)

// Computed
const isSelected = computed(() => {
  return timelineStore.selectedLayers.includes(props.item.id)
})

const duration = computed(() => {
  const startTime = props.item.startTime || props.item.timeRange?.start || 0
  const endTime = props.item.endTime || props.item.timeRange?.end || 0
  return endTime - startTime
})

const itemStyle = computed(() => {
  const startTime = props.item.startTime || props.item.timeRange?.start || 0
  const left = timelineStore.timeToPixel(startTime)
  const width = timelineStore.timeToPixel(duration.value)
  const minWidth = 20 // Minimum width in pixels

  return {
    left: left + 'px',
    width: Math.max(width, minWidth) + 'px',
    top: '5px',
    height: (timelineStore.trackHeight - 10) + 'px',
    backgroundColor: getItemColor(),
    border: `1px solid ${getItemBorderColor()}`,
    borderRadius: '4px',
    zIndex: isSelected.value ? 10 : 1,
    transform: (isDragging.value || isResizing.value) ? 'scale(1.01)' : 'scale(1)',
    transition: (isDragging.value || isResizing.value) ? 'none' : 'all 0.1s ease'
  }
})

const displayText = computed(() => {
  if (props.item.type === 'video') {
    return props.item.properties?.src ? 
      props.item.properties.src.split('/').pop() : 
      'Video Layer'
  }
  return props.item.name || 'Media Item'
})

const hasVideoSource = computed(() => {
  return props.item.properties?.src || props.item.type === 'video'
})

// Methods
const getItemColor = () => {
  if (isSelected.value) {
    return '#10b981' // Green for selected
  } else if (props.item.type === 'video') {
    return '#059669' // Dark green for video
  } else if (props.item.type === 'audio') {
    return '#0891b2' // Blue for audio
  } else if (props.item.type === 'image') {
    return '#7c3aed' // Purple for image
  } else {
    return '#6b7280' // Gray for other
  }
}

const getItemBorderColor = () => {
  if (isSelected.value) {
    return '#047857'
  } else if (isHovered.value) {
    return '#34d399'
  } else {
    return '#374151'
  }
}

const formatDuration = (seconds) => {
  if (!seconds || isNaN(seconds)) return '0.0s'
  return seconds.toFixed(1) + 's'
}

const getPlayButtonTitle = () => {
  return isPlayingVideo.value ? 'Pause Video' : 'Play Video'
}

// Video playback methods
const toggleVideoPlayback = async () => {
  try {
    if (isPlayingVideo.value) {
      isPlayingVideo.value = false
      emit('video-pause', props.item)
    } else {
      isPlayingVideo.value = true
      emit('video-play', props.item)
      
      // Sync timeline to this item's start time
      const startTime = props.item.startTime || props.item.timeRange?.start || 0
      if (Math.abs(timelineStore.currentTime - startTime) > 0.1) {
        timelineStore.setCurrentTime(startTime)
        emit('seek-to-time', startTime)
      }
    }
  } catch (error) {
    console.error('Error toggling video playback:', error)
    isPlayingVideo.value = false
  }
}

// Throttle function using requestAnimationFrame for better performance
const throttleRAF = (func) => {
  let rafId = null
  let lastArgs = null
  
  return function (...args) {
    lastArgs = args
    if (rafId === null) {
      rafId = requestAnimationFrame(() => {
        func.apply(this, lastArgs)
        rafId = null
      })
    }
  }
}

const handleMouseDown = (event) => {
  if (event.target.classList.contains('resize-handle')) return

  emit('select', props.item, event)

  if (event.button === 0) { // Left mouse button
    isDragging.value = true
    dragStartX.value = event.clientX

    emit('drag-start', props.item, event)

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
    document.body.style.cursor = 'grabbing'
    document.body.style.userSelect = 'none'
    
    // Prevent default to avoid text selection
    event.preventDefault()
  }
}

const handleMouseMove = (event) => {
  if (!isDragging.value) return

  const deltaX = event.clientX - dragStartX.value
  throttledDragEmit(deltaX)
}

const throttledDragEmit = throttleRAF((deltaX) => {
  emit('drag', deltaX)
})

const handleMouseUp = () => {
  if (isDragging.value) {
    isDragging.value = false
    emit('drag-end')

    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
    document.body.style.cursor = ''
    document.body.style.userSelect = ''
  }
}

const handleResizeStart = (edge, event) => {
  event.preventDefault()
  event.stopPropagation()

  isResizing.value = true
  emit('resize-start', props.item, edge, event)

  const handleResizeMove = (moveEvent) => {
    const deltaX = moveEvent.clientX - event.clientX
    throttledResizeEmit(deltaX)
  }

  const throttledResizeEmit = throttleRAF((deltaX) => {
    emit('resize', deltaX)
  })

  const handleResizeEnd = () => {
    isResizing.value = false
    emit('resize-end')
    document.removeEventListener('mousemove', handleResizeMove)
    document.removeEventListener('mouseup', handleResizeEnd)
    document.body.style.cursor = ''
    document.body.style.userSelect = ''
  }

  document.addEventListener('mousemove', handleResizeMove)
  document.addEventListener('mouseup', handleResizeEnd)
  document.body.style.cursor = 'ew-resize'
  document.body.style.userSelect = 'none'
}

const handleContextMenu = (event) => {
  emit('context-menu', event, props.item)
}

const handleResizeHover = (edge) => {
  document.body.style.cursor = 'ew-resize'
}

const handleResizeLeave = (edge) => {
  if (!isResizing.value) {
    document.body.style.cursor = ''
  }
}

// Lifecycle hooks
onMounted(() => {
  // Initialize video item if needed
})

onUnmounted(() => {
  // Cleanup if needed
})
</script>

<style scoped>
.video-track-item {
  transition: all 0.1s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.video-track-item:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
}

.video-track-item.selected {
  box-shadow: 0 0 0 2px #10b981, 0 2px 6px rgba(0, 0, 0, 0.4);
}

.video-track-item.dragging {
  opacity: 0.9;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.6);
  z-index: 100 !important;
}

.video-track-item.resizing {
  opacity: 0.9;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
  z-index: 100 !important;
}

.resize-handle {
  transition: all 0.15s ease;
  border-radius: 2px;
}

.video-track-item:hover .resize-handle {
  opacity: 0.7;
}

.resize-handle:hover {
  opacity: 1 !important;
  background-color: #34d399 !important;
  transform: scaleY(1.1);
}

.video-track-item.resizing .resize-handle {
  opacity: 1 !important;
  background-color: #10b981 !important;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-content {
  pointer-events: none;
}

/* Video controls */
.video-play-btn {
  pointer-events: auto;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.video-play-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
}

.video-play-btn:active {
  transform: scale(0.95);
}

/* Playing state indicator */
.video-track-item.playing {
  box-shadow: 0 0 0 2px #10b981, 0 2px 6px rgba(0, 0, 0, 0.4);
}

.video-track-item.playing .video-play-btn {
  background-color: #10b981;
}

.video-track-item.playing .video-play-btn:hover {
  background-color: #059669;
}
</style>
