<template>
  <div
    class="video-track absolute inset-0"
    :style="{ top: trackOffset + 'px' }"
  >
    <!-- Grid lines -->
    <div class="absolute inset-0 pointer-events-none">
      <div
        v-for="gridLine in gridLines"
        :key="gridLine"
        class="absolute top-0 bottom-0 w-px bg-gray-700 opacity-30"
        :style="{ left: gridLine + 'px' }"
      ></div>
    </div>

    <!-- Video Items -->
    <VideoTrackItem
      v-for="item in videoItems"
      :key="item.id"
      :item="item"
      @select="handleItemSelect"
      @drag-start="handleDragStart"
      @drag="handleDrag"
      @drag-end="handleDragEnd"
      @resize-start="handleResizeStart"
      @resize="handleResize"
      @resize-end="handleResizeEnd"
      @context-menu="handleContextMenu"
      @video-play="handleVideoPlay"
      @video-pause="handleVideoPause"
      @seek-to-time="handleSeekToTime"
    />

    <!-- Track label -->
    <div class="absolute left-2 top-2 text-xs text-gray-400 pointer-events-none">
      Video Track
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useTimelineStore } from '@/stores/timeline-store'
import { useVideoLayersStore } from '@/stores/video-layers-store'
import VideoTrackItem from './VideoTrackItem.vue'

const props = defineProps({
  trackOffset: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits([
  'context-menu',
  'item-action'
])

const timelineStore = useTimelineStore()
const videoLayersStore = useVideoLayersStore()

// Computed
const videoItems = computed(() => {
  // Get video layers from video layers store
  const videoLayers = videoLayersStore.layers.filter(layer => 
    layer.type === 'video' || layer.type === 'audio' || layer.type === 'image'
  )
  
  // Also get custom layers from timeline store
  const customLayers = timelineStore.layerItems || []
  
  return [...videoLayers, ...customLayers]
})

const gridLines = computed(() => {
  const lines = []
  const gridInterval = timelineStore.gridSize // Grid size in seconds
  const pixelsPerSecond = timelineStore.zoom * 100
  const totalWidth = timelineStore.totalTimelineWidth
  
  for (let time = 0; time <= timelineStore.duration; time += gridInterval) {
    const x = time * pixelsPerSecond
    if (x <= totalWidth) {
      lines.push(x)
    }
  }
  
  return lines
})

// Throttle function using requestAnimationFrame for better performance
const throttleRAF = (func) => {
  let rafId = null
  let lastArgs = null
  
  return function (...args) {
    lastArgs = args
    if (rafId === null) {
      rafId = requestAnimationFrame(() => {
        func.apply(this, lastArgs)
        rafId = null
      })
    }
  }
}

// Event handlers
const handleItemSelect = (item, event) => {
  if (event.ctrlKey || event.metaKey) {
    // Multi-select
    if (timelineStore.selectedLayers.includes(item.id)) {
      timelineStore.selectedLayers = timelineStore.selectedLayers.filter(id => id !== item.id)
    } else {
      timelineStore.selectedLayers.push(item.id)
    }
  } else {
    // Single select
    timelineStore.selectedLayers = [item.id]
    timelineStore.clearSelection() // Clear subtitle selection
  }
}

const handleDragStart = (item, event) => {
  timelineStore.saveState()

  if (!timelineStore.selectedLayers.includes(item.id)) {
    timelineStore.selectedLayers = [item.id]
  }

  const startTime = item.startTime || item.timeRange?.start || 0
  timelineStore.startDrag('move', item, event.clientX, startTime)
}

const handleDrag = (deltaX) => {
  if (!timelineStore.isDragging || timelineStore.dragType !== 'move') return

  // Use throttled update for better performance
  throttledDragUpdate(deltaX)
}

const throttledDragUpdate = throttleRAF((deltaX) => {
  const deltaTime = timelineStore.pixelToTime(deltaX)
  const newStartTime = Math.max(0, timelineStore.dragStartTime + deltaTime)

  // Snap to grid
  const snappedStartTime = timelineStore.snapToGrid ?
    timelineStore.getSnapTime(newStartTime) : newStartTime

  // Update all selected video items
  timelineStore.selectedLayers.forEach(itemId => {
    const item = videoItems.value.find(i => i.id === itemId)
    if (item) {
      const currentStartTime = item.startTime || item.timeRange?.start || 0
      const currentEndTime = item.endTime || item.timeRange?.end || 0
      const duration = currentEndTime - currentStartTime
      const newEndTime = snappedStartTime + duration

      updateItemTiming(item, snappedStartTime, newEndTime)
    }
  })
})

const handleDragEnd = () => {
  timelineStore.stopDrag()
}

const handleResizeStart = (item, edge, event) => {
  timelineStore.saveState()

  const resizeType = edge === 'left' ? 'resize-left' : 'resize-right'
  const startTime = edge === 'left' ? 
    (item.startTime || item.timeRange?.start || 0) : 
    (item.endTime || item.timeRange?.end || 0)

  timelineStore.startDrag(resizeType, item, event.clientX, startTime)
}

const handleResize = (deltaX) => {
  if (!timelineStore.isDragging || !timelineStore.dragType.startsWith('resize')) return

  // Use throttled update for better performance
  throttledResizeUpdate(deltaX)
}

const throttledResizeUpdate = throttleRAF((deltaX) => {
  const deltaTime = timelineStore.pixelToTime(deltaX)
  const item = timelineStore.dragItem

  if (timelineStore.dragType === 'resize-left') {
    const newStartTime = Math.max(0, timelineStore.dragStartTime + deltaTime)
    const snappedStartTime = timelineStore.snapToGrid ?
      timelineStore.getSnapTime(newStartTime) : newStartTime

    const currentEndTime = item.endTime || item.timeRange?.end || 0
    // Ensure minimum duration
    if (currentEndTime - snappedStartTime >= 0.1) {
      updateItemTiming(item, snappedStartTime, currentEndTime)
    }
  } else if (timelineStore.dragType === 'resize-right') {
    const currentStartTime = item.startTime || item.timeRange?.start || 0
    const newEndTime = Math.max(currentStartTime + 0.1, timelineStore.dragStartTime + deltaTime)
    const snappedEndTime = timelineStore.snapToGrid ?
      timelineStore.getSnapTime(newEndTime) : newEndTime

    updateItemTiming(item, currentStartTime, snappedEndTime)
  }
})

const handleResizeEnd = () => {
  timelineStore.stopDrag()
}

const handleContextMenu = (event, item) => {
  emit('context-menu', event, item)
}

// Video playback handlers
const handleVideoPlay = (item) => {
  console.log('Video play:', item)
  emit('item-action', 'video-play', { item })
}

const handleVideoPause = (item) => {
  console.log('Video pause:', item)
  emit('item-action', 'video-pause', { item })
}

const handleSeekToTime = (time) => {
  console.log('Seeking to time:', time)
  timelineStore.setCurrentTime(time)
  emit('item-action', 'seek-to-time', { time })
}

// Helper methods
const updateItemTiming = (item, startTime, endTime) => {
  if (item.timeRange) {
    // Video layers store format
    videoLayersStore.updateLayer(item.id, {
      timeRange: { start: startTime, end: endTime }
    })
  } else {
    // Timeline store format
    timelineStore.updateLayer(item.id, {
      startTime: startTime,
      endTime: endTime
    })
  }
}
</script>

<style scoped>
.video-track {
  background-color: rgba(16, 185, 129, 0.05);
  border-top: 1px solid rgba(16, 185, 129, 0.2);
  border-bottom: 1px solid rgba(16, 185, 129, 0.2);
}

.video-track:hover {
  background-color: rgba(16, 185, 129, 0.08);
}
</style>
